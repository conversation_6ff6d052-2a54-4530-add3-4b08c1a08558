{"id": "a81e5d6e-e420-4d1f-97be-181e5a9347c8", "prevId": "1fedba0b-3bfe-4ddd-9d71-8e7e9b99e018", "version": "7", "dialect": "postgresql", "tables": {"public.academic_years": {"name": "academic_years", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "year": {"name": "year", "type": "<PERSON><PERSON><PERSON>(4)", "primaryKey": false, "notNull": true}, "is_current_year": {"name": "is_current_year", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "session_id_fk": {"name": "session_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"academic_years_session_id_fk_sessions_id_fk": {"name": "academic_years_session_id_fk_sessions_id_fk", "tableFrom": "academic_years", "tableTo": "sessions", "columnsFrom": ["session_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.batches": {"name": "batches", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "academic_year_id_fk": {"name": "academic_year_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "course_id_fk": {"name": "course_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "class_id_fk": {"name": "class_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "section_id_fk": {"name": "section_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "shift_id_fk": {"name": "shift_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "session_id_fk": {"name": "session_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"batches_academic_year_id_fk_academic_years_id_fk": {"name": "batches_academic_year_id_fk_academic_years_id_fk", "tableFrom": "batches", "tableTo": "academic_years", "columnsFrom": ["academic_year_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "batches_course_id_fk_courses_id_fk": {"name": "batches_course_id_fk_courses_id_fk", "tableFrom": "batches", "tableTo": "courses", "columnsFrom": ["course_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "batches_class_id_fk_classes_id_fk": {"name": "batches_class_id_fk_classes_id_fk", "tableFrom": "batches", "tableTo": "classes", "columnsFrom": ["class_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "batches_section_id_fk_sections_id_fk": {"name": "batches_section_id_fk_sections_id_fk", "tableFrom": "batches", "tableTo": "sections", "columnsFrom": ["section_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "batches_shift_id_fk_shifts_id_fk": {"name": "batches_shift_id_fk_shifts_id_fk", "tableFrom": "batches", "tableTo": "shifts", "columnsFrom": ["shift_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "batches_session_id_fk_sessions_id_fk": {"name": "batches_session_id_fk_sessions_id_fk", "tableFrom": "batches", "tableTo": "sessions", "columnsFrom": ["session_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.batch_papers": {"name": "batch_papers", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "batch_id_fk": {"name": "batch_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "subject_metadata_id_fk": {"name": "subject_metadata_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"batch_papers_batch_id_fk_batches_id_fk": {"name": "batch_papers_batch_id_fk_batches_id_fk", "tableFrom": "batch_papers", "tableTo": "batches", "columnsFrom": ["batch_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "batch_papers_subject_metadata_id_fk_subject_metadatas_id_fk": {"name": "batch_papers_subject_metadata_id_fk_subject_metadatas_id_fk", "tableFrom": "batch_papers", "tableTo": "subject_metadatas", "columnsFrom": ["subject_metadata_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.classes": {"name": "classes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "class_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"classes_sequence_unique": {"name": "classes_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.courses": {"name": "courses", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "degree_id_fk": {"name": "degree_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "programme_type": {"name": "programme_type", "type": "programme_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "short_name": {"name": "short_name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "code_prefix": {"name": "code_prefix", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "university_code": {"name": "university_code", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": false}, "number_of_semesters": {"name": "number_of_semesters", "type": "integer", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"courses_degree_id_fk_degree_id_fk": {"name": "courses_degree_id_fk_degree_id_fk", "tableFrom": "courses", "tableTo": "degree", "columnsFrom": ["degree_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"courses_sequence_unique": {"name": "courses_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"documents_name_unique": {"name": "documents_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "documents_sequence_unique": {"name": "documents_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.marksheets": {"name": "marksheets", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "student_id_fk": {"name": "student_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "class_id_fk": {"name": "class_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "year": {"name": "year", "type": "integer", "primaryKey": false, "notNull": true}, "sgpa": {"name": "sgpa", "type": "numeric", "primaryKey": false, "notNull": false}, "cgpa": {"name": "cgpa", "type": "numeric", "primaryKey": false, "notNull": false}, "classification": {"name": "classification", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "remarks": {"name": "remarks", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "source": {"name": "source", "type": "marksheet_source", "typeSchema": "public", "primaryKey": false, "notNull": false}, "file": {"name": "file", "type": "<PERSON><PERSON><PERSON>(700)", "primaryKey": false, "notNull": false}, "created_by_user_id": {"name": "created_by_user_id", "type": "integer", "primaryKey": false, "notNull": true}, "updated_by_user_id": {"name": "updated_by_user_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"marksheets_student_id_fk_students_id_fk": {"name": "marksheets_student_id_fk_students_id_fk", "tableFrom": "marksheets", "tableTo": "students", "columnsFrom": ["student_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "marksheets_class_id_fk_classes_id_fk": {"name": "marksheets_class_id_fk_classes_id_fk", "tableFrom": "marksheets", "tableTo": "classes", "columnsFrom": ["class_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "marksheets_created_by_user_id_users_id_fk": {"name": "marksheets_created_by_user_id_users_id_fk", "tableFrom": "marksheets", "tableTo": "users", "columnsFrom": ["created_by_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "marksheets_updated_by_user_id_users_id_fk": {"name": "marksheets_updated_by_user_id_users_id_fk", "tableFrom": "marksheets", "tableTo": "users", "columnsFrom": ["updated_by_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.offered_subjects": {"name": "offered_subjects", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "subject_metadata_id_fk": {"name": "subject_metadata_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"offered_subjects_subject_metadata_id_fk_subject_metadatas_id_fk": {"name": "offered_subjects_subject_metadata_id_fk_subject_metadatas_id_fk", "tableFrom": "offered_subjects", "tableTo": "subject_metadatas", "columnsFrom": ["subject_metadata_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.papers": {"name": "papers", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "offered_subject_id": {"name": "offered_subject_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "short_name": {"name": "short_name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "mode": {"name": "mode", "type": "paper_mode_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "display_name": {"name": "display_name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"papers_offered_subject_id_offered_subjects_id_fk": {"name": "papers_offered_subject_id_offered_subjects_id_fk", "tableFrom": "papers", "tableTo": "offered_subjects", "columnsFrom": ["offered_subject_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sections": {"name": "sections", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sections_name_unique": {"name": "sections_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "sections_sequence_unique": {"name": "sections_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "from": {"name": "from", "type": "date", "primaryKey": false, "notNull": true}, "to": {"name": "to", "type": "date", "primaryKey": false, "notNull": true}, "is_current_session": {"name": "is_current_session", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "code_prefix": {"name": "code_prefix", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.shifts": {"name": "shifts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "code_prefix": {"name": "code_prefix", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"shifts_sequence_unique": {"name": "shifts_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_papers": {"name": "student_papers", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "student_id_fk": {"name": "student_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "batch_paper_id_fk": {"name": "batch_paper_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "batch_id_fk": {"name": "batch_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"student_papers_student_id_fk_students_id_fk": {"name": "student_papers_student_id_fk_students_id_fk", "tableFrom": "student_papers", "tableTo": "students", "columnsFrom": ["student_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_papers_batch_paper_id_fk_batch_papers_id_fk": {"name": "student_papers_batch_paper_id_fk_batch_papers_id_fk", "tableFrom": "student_papers", "tableTo": "batch_papers", "columnsFrom": ["batch_paper_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_papers_batch_id_fk_batches_id_fk": {"name": "student_papers_batch_id_fk_batches_id_fk", "tableFrom": "student_papers", "tableTo": "batches", "columnsFrom": ["batch_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.study_materials": {"name": "study_materials", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "availability": {"name": "availability", "type": "study_material_availability_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "subject_metadata_id_fk": {"name": "subject_metadata_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "session_di_fk": {"name": "session_di_fk", "type": "integer", "primaryKey": false, "notNull": false}, "course_id_fk": {"name": "course_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "batch_id_fk": {"name": "batch_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "study_material_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "variant": {"name": "variant", "type": "study_meta_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(700)", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": true}, "file_path": {"name": "file_path", "type": "<PERSON><PERSON><PERSON>(700)", "primaryKey": false, "notNull": false}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"study_materials_subject_metadata_id_fk_subject_metadatas_id_fk": {"name": "study_materials_subject_metadata_id_fk_subject_metadatas_id_fk", "tableFrom": "study_materials", "tableTo": "subject_metadatas", "columnsFrom": ["subject_metadata_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "study_materials_session_di_fk_sessions_id_fk": {"name": "study_materials_session_di_fk_sessions_id_fk", "tableFrom": "study_materials", "tableTo": "sessions", "columnsFrom": ["session_di_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "study_materials_course_id_fk_courses_id_fk": {"name": "study_materials_course_id_fk_courses_id_fk", "tableFrom": "study_materials", "tableTo": "courses", "columnsFrom": ["course_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "study_materials_batch_id_fk_batches_id_fk": {"name": "study_materials_batch_id_fk_batches_id_fk", "tableFrom": "study_materials", "tableTo": "batches", "columnsFrom": ["batch_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subjects": {"name": "subjects", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "marksheet_id_fk": {"name": "marksheet_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "subject_metadata_id_fk": {"name": "subject_metadata_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "year1": {"name": "year1", "type": "integer", "primaryKey": false, "notNull": true}, "year2": {"name": "year2", "type": "integer", "primaryKey": false, "notNull": false}, "internal_marks": {"name": "internal_marks", "type": "integer", "primaryKey": false, "notNull": false}, "internal_year": {"name": "internal_year", "type": "integer", "primaryKey": false, "notNull": false}, "internal_credit": {"name": "internal_credit", "type": "integer", "primaryKey": false, "notNull": false}, "practical_marks": {"name": "practical_marks", "type": "integer", "primaryKey": false, "notNull": false}, "practical_year": {"name": "practical_year", "type": "integer", "primaryKey": false, "notNull": false}, "practical_credit": {"name": "practical_credit", "type": "integer", "primaryKey": false, "notNull": false}, "theory_marks": {"name": "theory_marks", "type": "integer", "primaryKey": false, "notNull": false}, "theory_year": {"name": "theory_year", "type": "integer", "primaryKey": false, "notNull": false}, "theory_credit": {"name": "theory_credit", "type": "integer", "primaryKey": false, "notNull": false}, "total_marks": {"name": "total_marks", "type": "integer", "primaryKey": false, "notNull": false}, "vival_marks": {"name": "vival_marks", "type": "integer", "primaryKey": false, "notNull": false}, "vival_year": {"name": "vival_year", "type": "integer", "primaryKey": false, "notNull": false}, "vival_credit": {"name": "vival_credit", "type": "integer", "primaryKey": false, "notNull": false}, "project_marks": {"name": "project_marks", "type": "integer", "primaryKey": false, "notNull": false}, "project_year": {"name": "project_year", "type": "integer", "primaryKey": false, "notNull": false}, "project_credit": {"name": "project_credit", "type": "integer", "primaryKey": false, "notNull": false}, "total_credits": {"name": "total_credits", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "subject_status", "typeSchema": "public", "primaryKey": false, "notNull": false}, "ngp": {"name": "ngp", "type": "numeric", "primaryKey": false, "notNull": false}, "tgp": {"name": "tgp", "type": "numeric", "primaryKey": false, "notNull": false}, "letter_grade": {"name": "letter_grade", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"subjects_marksheet_id_fk_marksheets_id_fk": {"name": "subjects_marksheet_id_fk_marksheets_id_fk", "tableFrom": "subjects", "tableTo": "marksheets", "columnsFrom": ["marksheet_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "subjects_subject_metadata_id_fk_subject_metadatas_id_fk": {"name": "subjects_subject_metadata_id_fk_subject_metadatas_id_fk", "tableFrom": "subjects", "tableTo": "subject_metadatas", "columnsFrom": ["subject_metadata_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subject_metadatas": {"name": "subject_metadatas", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "degree_id_fk": {"name": "degree_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "programme_type": {"name": "programme_type", "type": "programme_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'HONOURS'"}, "framework": {"name": "framework", "type": "framework_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'CCF'"}, "class_id_fk": {"name": "class_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "specialization_id_fk": {"name": "specialization_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "subject_category_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "subject_type_id_fk": {"name": "subject_type_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "irp_name": {"name": "irp_name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "irp_code": {"name": "irp_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "marksheet_code": {"name": "marksheet_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "is_optional": {"name": "is_optional", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "credit": {"name": "credit", "type": "integer", "primaryKey": false, "notNull": false}, "theory_credit": {"name": "theory_credit", "type": "integer", "primaryKey": false, "notNull": false}, "full_marks_theory": {"name": "full_marks_theory", "type": "integer", "primaryKey": false, "notNull": false}, "practical_credit": {"name": "practical_credit", "type": "integer", "primaryKey": false, "notNull": false}, "full_marks_practical": {"name": "full_marks_practical", "type": "integer", "primaryKey": false, "notNull": false}, "internal_credit": {"name": "internal_credit", "type": "integer", "primaryKey": false, "notNull": false}, "full_marks_internal": {"name": "full_marks_internal", "type": "integer", "primaryKey": false, "notNull": false}, "project_credit": {"name": "project_credit", "type": "integer", "primaryKey": false, "notNull": false}, "full_marks_project": {"name": "full_marks_project", "type": "integer", "primaryKey": false, "notNull": false}, "vival_credit": {"name": "vival_credit", "type": "integer", "primaryKey": false, "notNull": false}, "full_marks_viva": {"name": "full_marks_viva", "type": "integer", "primaryKey": false, "notNull": false}, "full_marks": {"name": "full_marks", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"subject_metadatas_degree_id_fk_degree_id_fk": {"name": "subject_metadatas_degree_id_fk_degree_id_fk", "tableFrom": "subject_metadatas", "tableTo": "degree", "columnsFrom": ["degree_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "subject_metadatas_class_id_fk_classes_id_fk": {"name": "subject_metadatas_class_id_fk_classes_id_fk", "tableFrom": "subject_metadatas", "tableTo": "classes", "columnsFrom": ["class_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "subject_metadatas_specialization_id_fk_specializations_id_fk": {"name": "subject_metadatas_specialization_id_fk_specializations_id_fk", "tableFrom": "subject_metadatas", "tableTo": "specializations", "columnsFrom": ["specialization_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "subject_metadatas_subject_type_id_fk_subject_types_id_fk": {"name": "subject_metadatas_subject_type_id_fk_subject_types_id_fk", "tableFrom": "subject_metadatas", "tableTo": "subject_types", "columnsFrom": ["subject_type_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subject_types": {"name": "subject_types", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "irp_name": {"name": "irp_name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "irp_short_name": {"name": "irp_short_name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "marksheet_name": {"name": "marksheet_name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "marksheet_short_name": {"name": "marksheet_short_name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"subject_types_sequence_unique": {"name": "subject_types_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.academic_subjects": {"name": "academic_subjects", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "board_university_id_fk": {"name": "board_university_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "passing_marks": {"name": "passing_marks", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"academic_subjects_board_university_id_fk_board_universities_id_fk": {"name": "academic_subjects_board_university_id_fk_board_universities_id_fk", "tableFrom": "academic_subjects", "tableTo": "board_universities", "columnsFrom": ["board_university_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.admission_additional_info": {"name": "admission_additional_info", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "application_form_id_fk": {"name": "application_form_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "alternate_mobile_number": {"name": "alternate_mobile_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "blood_group_id_fk": {"name": "blood_group_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "religion_id_fk": {"name": "religion_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "category_id_fk": {"name": "category_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "is_physically_challenged": {"name": "is_physically_challenged", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "disability_type": {"name": "disability_type", "type": "disability_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "is_single_parent": {"name": "is_single_parent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "father_title": {"name": "father_title", "type": "person_title_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "father_name": {"name": "father_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "mother_title": {"name": "mother_title", "type": "person_title_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "mother_name": {"name": "mother_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_either_parent_staff": {"name": "is_either_parent_staff", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "name_of_staff_parent": {"name": "name_of_staff_parent", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "department_of_staff_parent_fk": {"name": "department_of_staff_parent_fk", "type": "integer", "primaryKey": false, "notNull": false}, "has_smartphone": {"name": "has_smartphone", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "has_laptop_or_desktop": {"name": "has_laptop_or_desktop", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "has_internet_access": {"name": "has_internet_access", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "annual_income_id_fk": {"name": "annual_income_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "apply_under_ncc_category": {"name": "apply_under_ncc_category", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "apply_under_sports_category": {"name": "apply_under_sports_category", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"admission_additional_info_application_form_id_fk_application_forms_id_fk": {"name": "admission_additional_info_application_form_id_fk_application_forms_id_fk", "tableFrom": "admission_additional_info", "tableTo": "application_forms", "columnsFrom": ["application_form_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_additional_info_blood_group_id_fk_blood_group_id_fk": {"name": "admission_additional_info_blood_group_id_fk_blood_group_id_fk", "tableFrom": "admission_additional_info", "tableTo": "blood_group", "columnsFrom": ["blood_group_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_additional_info_religion_id_fk_religion_id_fk": {"name": "admission_additional_info_religion_id_fk_religion_id_fk", "tableFrom": "admission_additional_info", "tableTo": "religion", "columnsFrom": ["religion_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_additional_info_category_id_fk_categories_id_fk": {"name": "admission_additional_info_category_id_fk_categories_id_fk", "tableFrom": "admission_additional_info", "tableTo": "categories", "columnsFrom": ["category_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_additional_info_department_of_staff_parent_fk_departments_id_fk": {"name": "admission_additional_info_department_of_staff_parent_fk_departments_id_fk", "tableFrom": "admission_additional_info", "tableTo": "departments", "columnsFrom": ["department_of_staff_parent_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_additional_info_annual_income_id_fk_annual_incomes_id_fk": {"name": "admission_additional_info_annual_income_id_fk_annual_incomes_id_fk", "tableFrom": "admission_additional_info", "tableTo": "annual_incomes", "columnsFrom": ["annual_income_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.admission_academic_info": {"name": "admission_academic_info", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "application_form_id_fk": {"name": "application_form_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "board_university_id_fk": {"name": "board_university_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "board_result_status": {"name": "board_result_status", "type": "board_result_status_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "roll_number": {"name": "roll_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "school_number": {"name": "school_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "center_number": {"name": "center_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "admit_card_id": {"name": "admit_card_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "institute_id_fk": {"name": "institute_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "other_institute": {"name": "other_institute", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "language_medium_id_fk": {"name": "language_medium_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "year_of_passing": {"name": "year_of_passing", "type": "integer", "primaryKey": false, "notNull": true}, "stream_type": {"name": "stream_type", "type": "stream_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "is_registered_for_ug_in_cu": {"name": "is_registered_for_ug_in_cu", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "cu_registration_number": {"name": "cu_registration_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "previously_registered_course_id_fk": {"name": "previously_registered_course_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "other_previously_registered_course": {"name": "other_previously_registered_course", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "previous_college_id_fk": {"name": "previous_college_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "other_college": {"name": "other_college", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"admission_academic_info_application_form_id_fk_application_forms_id_fk": {"name": "admission_academic_info_application_form_id_fk_application_forms_id_fk", "tableFrom": "admission_academic_info", "tableTo": "application_forms", "columnsFrom": ["application_form_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_academic_info_board_university_id_fk_board_universities_id_fk": {"name": "admission_academic_info_board_university_id_fk_board_universities_id_fk", "tableFrom": "admission_academic_info", "tableTo": "board_universities", "columnsFrom": ["board_university_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_academic_info_institute_id_fk_institutions_id_fk": {"name": "admission_academic_info_institute_id_fk_institutions_id_fk", "tableFrom": "admission_academic_info", "tableTo": "institutions", "columnsFrom": ["institute_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_academic_info_language_medium_id_fk_language_medium_id_fk": {"name": "admission_academic_info_language_medium_id_fk_language_medium_id_fk", "tableFrom": "admission_academic_info", "tableTo": "language_medium", "columnsFrom": ["language_medium_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_academic_info_previously_registered_course_id_fk_courses_id_fk": {"name": "admission_academic_info_previously_registered_course_id_fk_courses_id_fk", "tableFrom": "admission_academic_info", "tableTo": "courses", "columnsFrom": ["previously_registered_course_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_academic_info_previous_college_id_fk_institutions_id_fk": {"name": "admission_academic_info_previous_college_id_fk_institutions_id_fk", "tableFrom": "admission_academic_info", "tableTo": "institutions", "columnsFrom": ["previous_college_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.admission_course_applications": {"name": "admission_course_applications", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "application_form_id_fk": {"name": "application_form_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "admission_course_id_fk": {"name": "admission_course_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"admission_course_applications_application_form_id_fk_application_forms_id_fk": {"name": "admission_course_applications_application_form_id_fk_application_forms_id_fk", "tableFrom": "admission_course_applications", "tableTo": "application_forms", "columnsFrom": ["application_form_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_course_applications_admission_course_id_fk_admission_courses_id_fk": {"name": "admission_course_applications_admission_course_id_fk_admission_courses_id_fk", "tableFrom": "admission_course_applications", "tableTo": "admission_courses", "columnsFrom": ["admission_course_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.admission_courses": {"name": "admission_courses", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "admission_id_fk": {"name": "admission_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "course_id_fk": {"name": "course_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_closed": {"name": "is_closed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"admission_courses_admission_id_fk_admissions_id_fk": {"name": "admission_courses_admission_id_fk_admissions_id_fk", "tableFrom": "admission_courses", "tableTo": "admissions", "columnsFrom": ["admission_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_courses_course_id_fk_courses_id_fk": {"name": "admission_courses_course_id_fk_courses_id_fk", "tableFrom": "admission_courses", "tableTo": "courses", "columnsFrom": ["course_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.admission_general_info": {"name": "admission_general_info", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "application_form_id_fk": {"name": "application_form_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "middle_name": {"name": "middle_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "date", "primaryKey": false, "notNull": true}, "nationality_id_fk": {"name": "nationality_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "other_nationality": {"name": "other_nationality", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_gujarati": {"name": "is_gujarati", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "category_id_fk": {"name": "category_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "religion_id_fk": {"name": "religion_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "gender_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'MALE'"}, "degree_level": {"name": "degree_level", "type": "degree_level_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'UNDER_GRADUATE'"}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "whatsapp_number": {"name": "whatsapp_number", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false}, "mobile_number": {"name": "mobile_number", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "residence_of_kolkata": {"name": "residence_of_kolkata", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"admission_general_info_application_form_id_fk_application_forms_id_fk": {"name": "admission_general_info_application_form_id_fk_application_forms_id_fk", "tableFrom": "admission_general_info", "tableTo": "application_forms", "columnsFrom": ["application_form_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_general_info_nationality_id_fk_nationality_id_fk": {"name": "admission_general_info_nationality_id_fk_nationality_id_fk", "tableFrom": "admission_general_info", "tableTo": "nationality", "columnsFrom": ["nationality_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_general_info_category_id_fk_categories_id_fk": {"name": "admission_general_info_category_id_fk_categories_id_fk", "tableFrom": "admission_general_info", "tableTo": "categories", "columnsFrom": ["category_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "admission_general_info_religion_id_fk_religion_id_fk": {"name": "admission_general_info_religion_id_fk_religion_id_fk", "tableFrom": "admission_general_info", "tableTo": "religion", "columnsFrom": ["religion_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.admissions": {"name": "admissions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "academic_year_id_fk": {"name": "academic_year_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "admission_code": {"name": "admission_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_closed": {"name": "is_closed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false}, "last_date": {"name": "last_date", "type": "date", "primaryKey": false, "notNull": false}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"admissions_academic_year_id_fk_academic_years_id_fk": {"name": "admissions_academic_year_id_fk_academic_years_id_fk", "tableFrom": "admissions", "tableTo": "academic_years", "columnsFrom": ["academic_year_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.application_forms": {"name": "application_forms", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "admission_id_fk": {"name": "admission_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "application_number": {"name": "application_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "form_status": {"name": "form_status", "type": "admission_form_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "admission_step": {"name": "admission_step", "type": "admission_steps", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"application_forms_admission_id_fk_admissions_id_fk": {"name": "application_forms_admission_id_fk_admissions_id_fk", "tableFrom": "application_forms", "tableTo": "admissions", "columnsFrom": ["admission_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sports_categories": {"name": "sports_categories", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sports_info": {"name": "sports_info", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "additional_info_id_fk": {"name": "additional_info_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "sports_category_id_fk": {"name": "sports_category_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "level": {"name": "level", "type": "sports_level", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"sports_info_additional_info_id_fk_admission_additional_info_id_fk": {"name": "sports_info_additional_info_id_fk_admission_additional_info_id_fk", "tableFrom": "sports_info", "tableTo": "admission_additional_info", "columnsFrom": ["additional_info_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "sports_info_sports_category_id_fk_sports_categories_id_fk": {"name": "sports_info_sports_category_id_fk_sports_categories_id_fk", "tableFrom": "sports_info", "tableTo": "sports_categories", "columnsFrom": ["sports_category_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_academic_subjects": {"name": "student_academic_subjects", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "admission_academic_info_id_fk": {"name": "admission_academic_info_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "academic_subject_id_fk": {"name": "academic_subject_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "full_marks": {"name": "full_marks", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "total_marks": {"name": "total_marks", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "result_status": {"name": "result_status", "type": "board_result_status_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"student_academic_subjects_admission_academic_info_id_fk_admission_academic_info_id_fk": {"name": "student_academic_subjects_admission_academic_info_id_fk_admission_academic_info_id_fk", "tableFrom": "student_academic_subjects", "tableTo": "admission_academic_info", "columnsFrom": ["admission_academic_info_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_academic_subjects_academic_subject_id_fk_academic_subjects_id_fk": {"name": "student_academic_subjects_academic_subject_id_fk_academic_subjects_id_fk", "tableFrom": "student_academic_subjects", "tableTo": "academic_subjects", "columnsFrom": ["academic_subject_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.apps": {"name": "apps", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(700)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.otps": {"name": "otps", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "otp": {"name": "otp", "type": "<PERSON><PERSON><PERSON>(6)", "primaryKey": false, "notNull": true}, "recipient": {"name": "recipient", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "otp_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.addons": {"name": "addons", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fees_components": {"name": "fees_components", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "fees_structure_id_fk": {"name": "fees_structure_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "fees_head_id_fk": {"name": "fees_head_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "is_concession_applicable": {"name": "is_concession_applicable", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "base_amount": {"name": "base_amount", "type": "double precision", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": true}, "remarks": {"name": "remarks", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"fees_components_fees_structure_id_fk_fees_structures_id_fk": {"name": "fees_components_fees_structure_id_fk_fees_structures_id_fk", "tableFrom": "fees_components", "tableTo": "fees_structures", "columnsFrom": ["fees_structure_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "fees_components_fees_head_id_fk_fees_heads_id_fk": {"name": "fees_components_fees_head_id_fk_fees_heads_id_fk", "tableFrom": "fees_components", "tableTo": "fees_heads", "columnsFrom": ["fees_head_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fees_heads": {"name": "fees_heads", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": true}, "remarks": {"name": "remarks", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"fees_heads_sequence_unique": {"name": "fees_heads_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fees_receipt_types": {"name": "fees_receipt_types", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "chk": {"name": "chk", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "chk_misc": {"name": "chk_misc", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "print_chln": {"name": "print_chln", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "spl_type": {"name": "spl_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "add_on_id": {"name": "add_on_id", "type": "integer", "primaryKey": false, "notNull": false}, "print_receipt": {"name": "print_receipt", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "chk_online": {"name": "chk_online", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "chk_on_sequence": {"name": "chk_on_sequence", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"fees_receipt_types_add_on_id_addons_id_fk": {"name": "fees_receipt_types_add_on_id_addons_id_fk", "tableFrom": "fees_receipt_types", "tableTo": "addons", "columnsFrom": ["add_on_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fees_slab_mapping": {"name": "fees_slab_mapping", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "fees_structure_id_fk": {"name": "fees_structure_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "fees_slab_id_fk": {"name": "fees_slab_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "fee_concession_rate": {"name": "fee_concession_rate", "type": "double precision", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"fees_slab_mapping_fees_structure_id_fk_fees_structures_id_fk": {"name": "fees_slab_mapping_fees_structure_id_fk_fees_structures_id_fk", "tableFrom": "fees_slab_mapping", "tableTo": "fees_structures", "columnsFrom": ["fees_structure_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "fees_slab_mapping_fees_slab_id_fk_fees_slab_id_fk": {"name": "fees_slab_mapping_fees_slab_id_fk_fees_slab_id_fk", "tableFrom": "fees_slab_mapping", "tableTo": "fees_slab", "columnsFrom": ["fees_slab_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fees_slab": {"name": "fees_slab", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"fees_slab_sequence_unique": {"name": "fees_slab_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fees_structures": {"name": "fees_structures", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "fees_receipt_type_id_fk": {"name": "fees_receipt_type_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "closing_date": {"name": "closing_date", "type": "date", "primaryKey": false, "notNull": true}, "academic_year_id_fk": {"name": "academic_year_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "course_id_fk": {"name": "course_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "class_id_fk": {"name": "class_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "shift_id_fk": {"name": "shift_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "advance_for_course_id_fk": {"name": "advance_for_course_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "advance_for_semester": {"name": "advance_for_semester", "type": "integer", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": true}, "online_start_date": {"name": "online_start_date", "type": "date", "primaryKey": false, "notNull": true}, "online_end_date": {"name": "online_end_date", "type": "date", "primaryKey": false, "notNull": true}, "number_of_instalments": {"name": "number_of_instalments", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"fees_structures_fees_receipt_type_id_fk_fees_receipt_types_id_fk": {"name": "fees_structures_fees_receipt_type_id_fk_fees_receipt_types_id_fk", "tableFrom": "fees_structures", "tableTo": "fees_receipt_types", "columnsFrom": ["fees_receipt_type_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "fees_structures_academic_year_id_fk_academic_years_id_fk": {"name": "fees_structures_academic_year_id_fk_academic_years_id_fk", "tableFrom": "fees_structures", "tableTo": "academic_years", "columnsFrom": ["academic_year_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "fees_structures_course_id_fk_courses_id_fk": {"name": "fees_structures_course_id_fk_courses_id_fk", "tableFrom": "fees_structures", "tableTo": "courses", "columnsFrom": ["course_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "fees_structures_class_id_fk_classes_id_fk": {"name": "fees_structures_class_id_fk_classes_id_fk", "tableFrom": "fees_structures", "tableTo": "classes", "columnsFrom": ["class_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "fees_structures_shift_id_fk_shifts_id_fk": {"name": "fees_structures_shift_id_fk_shifts_id_fk", "tableFrom": "fees_structures", "tableTo": "shifts", "columnsFrom": ["shift_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "fees_structures_advance_for_course_id_fk_courses_id_fk": {"name": "fees_structures_advance_for_course_id_fk_courses_id_fk", "tableFrom": "fees_structures", "tableTo": "courses", "columnsFrom": ["advance_for_course_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.instalments": {"name": "instalments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "fees_structure_id_fk": {"name": "fees_structure_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "instalment_number": {"name": "instalment_number", "type": "integer", "primaryKey": false, "notNull": true}, "base_amount": {"name": "base_amount", "type": "double precision", "primaryKey": false, "notNull": true, "default": 0}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "online_start_date": {"name": "online_start_date", "type": "date", "primaryKey": false, "notNull": false}, "online_end_date": {"name": "online_end_date", "type": "date", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"instalments_fees_structure_id_fk_fees_structures_id_fk": {"name": "instalments_fees_structure_id_fk_fees_structures_id_fk", "tableFrom": "instalments", "tableTo": "fees_structures", "columnsFrom": ["fees_structure_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_fees_mappings": {"name": "student_fees_mappings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "student_id_fk": {"name": "student_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "fees_structure_id_fk": {"name": "fees_structure_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "student_fees_mapping_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'FULL'"}, "instalment_id_fk": {"name": "instalment_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "base_amount": {"name": "base_amount", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "late_fee": {"name": "late_fee", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_payable": {"name": "total_payable", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "amount_paid": {"name": "amount_paid", "type": "integer", "primaryKey": false, "notNull": false}, "payment_status": {"name": "payment_status", "type": "payment_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "payment_mode": {"name": "payment_mode", "type": "payment_mode", "typeSchema": "public", "primaryKey": false, "notNull": false}, "transaction_ref": {"name": "transaction_ref", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "transaction_date": {"name": "transaction_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "receipt_number": {"name": "receipt_number", "type": "<PERSON><PERSON><PERSON>(2555)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"student_fees_mappings_student_id_fk_students_id_fk": {"name": "student_fees_mappings_student_id_fk_students_id_fk", "tableFrom": "student_fees_mappings", "tableTo": "students", "columnsFrom": ["student_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_fees_mappings_fees_structure_id_fk_fees_structures_id_fk": {"name": "student_fees_mappings_fees_structure_id_fk_fees_structures_id_fk", "tableFrom": "student_fees_mappings", "tableTo": "fees_structures", "columnsFrom": ["fees_structure_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_fees_mappings_instalment_id_fk_instalments_id_fk": {"name": "student_fees_mappings_instalment_id_fk_instalments_id_fk", "tableFrom": "student_fees_mappings", "tableTo": "instalments", "columnsFrom": ["instalment_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payments": {"name": "payments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "application_form_id_fk": {"name": "application_form_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "order_id": {"name": "order_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "transaction_id": {"name": "transaction_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "payment_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "payment_mode": {"name": "payment_mode", "type": "payment_mode", "typeSchema": "public", "primaryKey": false, "notNull": false}, "bank_txn_id": {"name": "bank_txn_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "gateway_name": {"name": "gateway_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "txn_date": {"name": "txn_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"payments_application_form_id_fk_application_forms_id_fk": {"name": "payments_application_form_id_fk_application_forms_id_fk", "tableFrom": "payments", "tableTo": "application_forms", "columnsFrom": ["application_form_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.annual_incomes": {"name": "annual_incomes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "range": {"name": "range", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"annual_incomes_sequence_unique": {"name": "annual_incomes_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.blood_group": {"name": "blood_group", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"blood_group_type_unique": {"name": "blood_group_type_unique", "nullsNotDistinct": false, "columns": ["type"]}, "blood_group_sequence_unique": {"name": "blood_group_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.board_result_status": {"name": "board_result_status", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "spcl_type": {"name": "spcl_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "result": {"name": "result", "type": "board_result_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"board_result_status_sequence_unique": {"name": "board_result_status_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.board_universities": {"name": "board_universities", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(700)", "primaryKey": false, "notNull": true}, "degree_id": {"name": "degree_id", "type": "integer", "primaryKey": false, "notNull": false}, "passing_marks": {"name": "passing_marks", "type": "integer", "primaryKey": false, "notNull": false}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address_id": {"name": "address_id", "type": "integer", "primaryKey": false, "notNull": false}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"board_universities_degree_id_degree_id_fk": {"name": "board_universities_degree_id_degree_id_fk", "tableFrom": "board_universities", "tableTo": "degree", "columnsFrom": ["degree_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "board_universities_address_id_address_id_fk": {"name": "board_universities_address_id_address_id_fk", "tableFrom": "board_universities", "tableTo": "address", "columnsFrom": ["address_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"board_universities_name_unique": {"name": "board_universities_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "board_universities_sequence_unique": {"name": "board_universities_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "document_required": {"name": "document_required", "type": "boolean", "primaryKey": false, "notNull": false}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"categories_name_unique": {"name": "categories_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "categories_code_unique": {"name": "categories_code_unique", "nullsNotDistinct": false, "columns": ["code"]}, "categories_sequence_unique": {"name": "categories_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.cities": {"name": "cities", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "state_id": {"name": "state_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "document_required": {"name": "document_required", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"cities_state_id_states_id_fk": {"name": "cities_state_id_states_id_fk", "tableFrom": "cities", "tableTo": "states", "columnsFrom": ["state_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"cities_name_unique": {"name": "cities_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "cities_code_unique": {"name": "cities_code_unique", "nullsNotDistinct": false, "columns": ["code"]}, "cities_sequence_unique": {"name": "cities_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.countries": {"name": "countries", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"countries_name_unique": {"name": "countries_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "countries_sequence_unique": {"name": "countries_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.degree": {"name": "degree", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "level": {"name": "level", "type": "degree_level_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"degree_name_unique": {"name": "degree_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "degree_sequence_unique": {"name": "degree_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.institutions": {"name": "institutions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(700)", "primaryKey": false, "notNull": true}, "degree_id": {"name": "degree_id", "type": "integer", "primaryKey": false, "notNull": true}, "address_id": {"name": "address_id", "type": "integer", "primaryKey": false, "notNull": false}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"institutions_degree_id_degree_id_fk": {"name": "institutions_degree_id_degree_id_fk", "tableFrom": "institutions", "tableTo": "degree", "columnsFrom": ["degree_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "institutions_address_id_address_id_fk": {"name": "institutions_address_id_address_id_fk", "tableFrom": "institutions", "tableTo": "address", "columnsFrom": ["address_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"institutions_name_unique": {"name": "institutions_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "institutions_sequence_unique": {"name": "institutions_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.language_medium": {"name": "language_medium", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"language_medium_name_unique": {"name": "language_medium_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "language_medium_sequence_unique": {"name": "language_medium_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.nationality": {"name": "nationality", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "integer", "primaryKey": false, "notNull": false}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"nationality_sequence_unique": {"name": "nationality_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.occupations": {"name": "occupations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"occupations_name_unique": {"name": "occupations_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "occupations_sequence_unique": {"name": "occupations_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pickup_point": {"name": "pickup_point", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.qualifications": {"name": "qualifications", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"qualifications_name_unique": {"name": "qualifications_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "qualifications_sequence_unique": {"name": "qualifications_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.religion": {"name": "religion", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"religion_name_unique": {"name": "religion_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "religion_sequence_unique": {"name": "religion_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.states": {"name": "states", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "country_id": {"name": "country_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"states_country_id_countries_id_fk": {"name": "states_country_id_countries_id_fk", "tableFrom": "states", "tableTo": "countries", "columnsFrom": ["country_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"states_name_unique": {"name": "states_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "states_sequence_unique": {"name": "states_sequence_unique", "nullsNotDistinct": false, "columns": ["sequence"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.transport": {"name": "transport", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "route_name": {"name": "route_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "mode": {"name": "mode", "type": "transport_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'OTHER'"}, "vehicle_number": {"name": "vehicle_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "driver_name": {"name": "driver_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "provider_details": {"name": "provider_details", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.academic_history": {"name": "academic_history", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "student_id_fk": {"name": "student_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "last_institution_id_fk": {"name": "last_institution_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "last_board_university_id_fk": {"name": "last_board_university_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "studied_up_to_class": {"name": "studied_up_to_class", "type": "integer", "primaryKey": false, "notNull": false}, "passed_year": {"name": "passed_year", "type": "integer", "primaryKey": false, "notNull": false}, "specialization_id": {"name": "specialization_id", "type": "integer", "primaryKey": false, "notNull": false}, "last_result_id_fk": {"name": "last_result_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "remarks": {"name": "remarks", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"academic_history_student_id_fk_students_id_fk": {"name": "academic_history_student_id_fk_students_id_fk", "tableFrom": "academic_history", "tableTo": "students", "columnsFrom": ["student_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "academic_history_last_institution_id_fk_institutions_id_fk": {"name": "academic_history_last_institution_id_fk_institutions_id_fk", "tableFrom": "academic_history", "tableTo": "institutions", "columnsFrom": ["last_institution_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "academic_history_last_board_university_id_fk_board_universities_id_fk": {"name": "academic_history_last_board_university_id_fk_board_universities_id_fk", "tableFrom": "academic_history", "tableTo": "board_universities", "columnsFrom": ["last_board_university_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "academic_history_specialization_id_specializations_id_fk": {"name": "academic_history_specialization_id_specializations_id_fk", "tableFrom": "academic_history", "tableTo": "specializations", "columnsFrom": ["specialization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "academic_history_last_result_id_fk_board_result_status_id_fk": {"name": "academic_history_last_result_id_fk_board_result_status_id_fk", "tableFrom": "academic_history", "tableTo": "board_result_status", "columnsFrom": ["last_result_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.academic_identifiers": {"name": "academic_identifiers", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "student_id_fk": {"name": "student_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "rfid": {"name": "rfid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "framework": {"name": "framework", "type": "framework_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "course_id_fk": {"name": "course_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "shift_id_fk": {"name": "shift_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "cu_form_number": {"name": "cu_form_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "uid": {"name": "uid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "old_uid": {"name": "old_uid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "registration_number": {"name": "registration_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "roll_number": {"name": "roll_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "section_id_fk": {"name": "section_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "class_roll_number": {"name": "class_roll_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "apaar_id": {"name": "apaar_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "abc_id": {"name": "abc_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "apprid": {"name": "apprid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "check_repeat": {"name": "check_repeat", "type": "boolean", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"academic_identifiers_student_id_fk_students_id_fk": {"name": "academic_identifiers_student_id_fk_students_id_fk", "tableFrom": "academic_identifiers", "tableTo": "students", "columnsFrom": ["student_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "academic_identifiers_course_id_fk_courses_id_fk": {"name": "academic_identifiers_course_id_fk_courses_id_fk", "tableFrom": "academic_identifiers", "tableTo": "courses", "columnsFrom": ["course_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "academic_identifiers_shift_id_fk_shifts_id_fk": {"name": "academic_identifiers_shift_id_fk_shifts_id_fk", "tableFrom": "academic_identifiers", "tableTo": "shifts", "columnsFrom": ["shift_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "academic_identifiers_section_id_fk_sections_id_fk": {"name": "academic_identifiers_section_id_fk_sections_id_fk", "tableFrom": "academic_identifiers", "tableTo": "sections", "columnsFrom": ["section_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"academic_identifiers_student_id_fk_unique": {"name": "academic_identifiers_student_id_fk_unique", "nullsNotDistinct": false, "columns": ["student_id_fk"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.accommodation": {"name": "accommodation", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "student_id_fk": {"name": "student_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "place_of_stay": {"name": "place_of_stay", "type": "place_of_stay_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "address_id_fk": {"name": "address_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"accommodation_student_id_fk_students_id_fk": {"name": "accommodation_student_id_fk_students_id_fk", "tableFrom": "accommodation", "tableTo": "students", "columnsFrom": ["student_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "accommodation_address_id_fk_address_id_fk": {"name": "accommodation_address_id_fk_address_id_fk", "tableFrom": "accommodation", "tableTo": "address", "columnsFrom": ["address_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"accommodation_student_id_fk_unique": {"name": "accommodation_student_id_fk_unique", "nullsNotDistinct": false, "columns": ["student_id_fk"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.address": {"name": "address", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "country_id_fk": {"name": "country_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "state_id_fk": {"name": "state_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "city_id_fk": {"name": "city_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "address_line": {"name": "address_line", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "landmark": {"name": "landmark", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "locality_type": {"name": "locality_type", "type": "locality_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "pincode": {"name": "pincode", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"address_country_id_fk_countries_id_fk": {"name": "address_country_id_fk_countries_id_fk", "tableFrom": "address", "tableTo": "countries", "columnsFrom": ["country_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "address_state_id_fk_states_id_fk": {"name": "address_state_id_fk_states_id_fk", "tableFrom": "address", "tableTo": "states", "columnsFrom": ["state_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "address_city_id_fk_cities_id_fk": {"name": "address_city_id_fk_cities_id_fk", "tableFrom": "address", "tableTo": "cities", "columnsFrom": ["city_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.departments": {"name": "departments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(900)", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(2000)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"departments_name_unique": {"name": "departments_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "departments_code_unique": {"name": "departments_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.disability_codes": {"name": "disability_codes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"disability_codes_code_unique": {"name": "disability_codes_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.emergency_contacts": {"name": "emergency_contacts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "student_id_fk": {"name": "student_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "person_name": {"name": "person_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "relation_to_student": {"name": "relation_to_student", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "office_phone": {"name": "office_phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "residential_phone": {"name": "residential_phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"emergency_contacts_student_id_fk_students_id_fk": {"name": "emergency_contacts_student_id_fk_students_id_fk", "tableFrom": "emergency_contacts", "tableTo": "students", "columnsFrom": ["student_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.family_details": {"name": "family_details", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "student_id_fk": {"name": "student_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "parent_type": {"name": "parent_type", "type": "parent_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "father_details_person_id_fk": {"name": "father_details_person_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "mother_details_person_id_fk": {"name": "mother_details_person_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "guardian_details_person_id_fk": {"name": "guardian_details_person_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "annual_income_id_fk": {"name": "annual_income_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"family_details_student_id_fk_students_id_fk": {"name": "family_details_student_id_fk_students_id_fk", "tableFrom": "family_details", "tableTo": "students", "columnsFrom": ["student_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "family_details_father_details_person_id_fk_person_id_fk": {"name": "family_details_father_details_person_id_fk_person_id_fk", "tableFrom": "family_details", "tableTo": "person", "columnsFrom": ["father_details_person_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "family_details_mother_details_person_id_fk_person_id_fk": {"name": "family_details_mother_details_person_id_fk_person_id_fk", "tableFrom": "family_details", "tableTo": "person", "columnsFrom": ["mother_details_person_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "family_details_guardian_details_person_id_fk_person_id_fk": {"name": "family_details_guardian_details_person_id_fk_person_id_fk", "tableFrom": "family_details", "tableTo": "person", "columnsFrom": ["guardian_details_person_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "family_details_annual_income_id_fk_annual_incomes_id_fk": {"name": "family_details_annual_income_id_fk_annual_incomes_id_fk", "tableFrom": "family_details", "tableTo": "annual_incomes", "columnsFrom": ["annual_income_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"family_details_student_id_fk_unique": {"name": "family_details_student_id_fk_unique", "nullsNotDistinct": false, "columns": ["student_id_fk"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.health": {"name": "health", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "student_id_fk": {"name": "student_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "blood_group_id_fk": {"name": "blood_group_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "eye_power_left": {"name": "eye_power_left", "type": "numeric", "primaryKey": false, "notNull": false}, "eye_power_right": {"name": "eye_power_right", "type": "numeric", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "past_medical_history": {"name": "past_medical_history", "type": "text", "primaryKey": false, "notNull": false}, "past_surgical_history": {"name": "past_surgical_history", "type": "text", "primaryKey": false, "notNull": false}, "drug_allergy": {"name": "drug_allergy", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"health_student_id_fk_students_id_fk": {"name": "health_student_id_fk_students_id_fk", "tableFrom": "health", "tableTo": "students", "columnsFrom": ["student_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "health_blood_group_id_fk_blood_group_id_fk": {"name": "health_blood_group_id_fk_blood_group_id_fk", "tableFrom": "health", "tableTo": "blood_group", "columnsFrom": ["blood_group_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"health_student_id_fk_unique": {"name": "health_student_id_fk_unique", "nullsNotDistinct": false, "columns": ["student_id_fk"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.person": {"name": "person", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "aadhaar_card_number": {"name": "a<PERSON><PERSON><PERSON>_card_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "qualification_id_fk": {"name": "qualification_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "occupation_id_fk": {"name": "occupation_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "office_addres_id_fk": {"name": "office_addres_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "office_phone": {"name": "office_phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"person_qualification_id_fk_qualifications_id_fk": {"name": "person_qualification_id_fk_qualifications_id_fk", "tableFrom": "person", "tableTo": "qualifications", "columnsFrom": ["qualification_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "person_occupation_id_fk_occupations_id_fk": {"name": "person_occupation_id_fk_occupations_id_fk", "tableFrom": "person", "tableTo": "occupations", "columnsFrom": ["occupation_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "person_office_addres_id_fk_address_id_fk": {"name": "person_office_addres_id_fk_address_id_fk", "tableFrom": "person", "tableTo": "address", "columnsFrom": ["office_addres_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.personal_details": {"name": "personal_details", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "student_id_fk": {"name": "student_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "nationality_id_fk": {"name": "nationality_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "other_nationality_id_fk": {"name": "other_nationality_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "aadhaar_card_number": {"name": "a<PERSON><PERSON><PERSON>_card_number", "type": "<PERSON><PERSON><PERSON>(16)", "primaryKey": false, "notNull": false}, "religion_id_fk": {"name": "religion_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "category_id_fk": {"name": "category_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "mother_tongue_language_medium_id_fk": {"name": "mother_tongue_language_medium_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "date", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "gender_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "alternative_email": {"name": "alternative_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "mailing_address_id_fk": {"name": "mailing_address_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "residential_address_id_fk": {"name": "residential_address_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "disability": {"name": "disability", "type": "disability_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "disablity_code_id_fk": {"name": "disablity_code_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"personal_details_student_id_fk_students_id_fk": {"name": "personal_details_student_id_fk_students_id_fk", "tableFrom": "personal_details", "tableTo": "students", "columnsFrom": ["student_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "personal_details_nationality_id_fk_nationality_id_fk": {"name": "personal_details_nationality_id_fk_nationality_id_fk", "tableFrom": "personal_details", "tableTo": "nationality", "columnsFrom": ["nationality_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "personal_details_other_nationality_id_fk_nationality_id_fk": {"name": "personal_details_other_nationality_id_fk_nationality_id_fk", "tableFrom": "personal_details", "tableTo": "nationality", "columnsFrom": ["other_nationality_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "personal_details_religion_id_fk_religion_id_fk": {"name": "personal_details_religion_id_fk_religion_id_fk", "tableFrom": "personal_details", "tableTo": "religion", "columnsFrom": ["religion_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "personal_details_category_id_fk_categories_id_fk": {"name": "personal_details_category_id_fk_categories_id_fk", "tableFrom": "personal_details", "tableTo": "categories", "columnsFrom": ["category_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "personal_details_mother_tongue_language_medium_id_fk_language_medium_id_fk": {"name": "personal_details_mother_tongue_language_medium_id_fk_language_medium_id_fk", "tableFrom": "personal_details", "tableTo": "language_medium", "columnsFrom": ["mother_tongue_language_medium_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "personal_details_mailing_address_id_fk_address_id_fk": {"name": "personal_details_mailing_address_id_fk_address_id_fk", "tableFrom": "personal_details", "tableTo": "address", "columnsFrom": ["mailing_address_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "personal_details_residential_address_id_fk_address_id_fk": {"name": "personal_details_residential_address_id_fk_address_id_fk", "tableFrom": "personal_details", "tableTo": "address", "columnsFrom": ["residential_address_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "personal_details_disablity_code_id_fk_disability_codes_id_fk": {"name": "personal_details_disablity_code_id_fk_disability_codes_id_fk", "tableFrom": "personal_details", "tableTo": "disability_codes", "columnsFrom": ["disablity_code_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.specializations": {"name": "specializations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"specializations_name_unique": {"name": "specializations_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.students": {"name": "students", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id_fk": {"name": "user_id_fk", "type": "integer", "primaryKey": false, "notNull": true}, "application_id_fk": {"name": "application_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "community": {"name": "community", "type": "community_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "handicapped": {"name": "handicapped", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "specialization_id_fk": {"name": "specialization_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "last_passed_year": {"name": "last_passed_year", "type": "integer", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false}, "alumni": {"name": "alumni", "type": "boolean", "primaryKey": false, "notNull": false}, "is_suspended": {"name": "is_suspended", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "leaving_date": {"name": "leaving_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "leaving_reason": {"name": "leaving_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"students_user_id_fk_users_id_fk": {"name": "students_user_id_fk_users_id_fk", "tableFrom": "students", "tableTo": "users", "columnsFrom": ["user_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "students_application_id_fk_application_forms_id_fk": {"name": "students_application_id_fk_application_forms_id_fk", "tableFrom": "students", "tableTo": "application_forms", "columnsFrom": ["application_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "students_specialization_id_fk_specializations_id_fk": {"name": "students_specialization_id_fk_specializations_id_fk", "tableFrom": "students", "tableTo": "specializations", "columnsFrom": ["specialization_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.transport_details": {"name": "transport_details", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "student_id_fk": {"name": "student_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "transport_id_fk": {"name": "transport_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "pickup_point_id_fk": {"name": "pickup_point_id_fk", "type": "integer", "primaryKey": false, "notNull": false}, "seat_number": {"name": "seat_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "pickup_time": {"name": "pickup_time", "type": "time", "primaryKey": false, "notNull": false}, "drop_off_time": {"name": "drop_off_time", "type": "time", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"transport_details_student_id_fk_students_id_fk": {"name": "transport_details_student_id_fk_students_id_fk", "tableFrom": "transport_details", "tableTo": "students", "columnsFrom": ["student_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transport_details_transport_id_fk_transport_id_fk": {"name": "transport_details_transport_id_fk_transport_id_fk", "tableFrom": "transport_details", "tableTo": "transport", "columnsFrom": ["transport_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transport_details_pickup_point_id_fk_pickup_point_id_fk": {"name": "transport_details_pickup_point_id_fk_pickup_point_id_fk", "tableFrom": "transport_details", "tableTo": "pickup_point", "columnsFrom": ["pickup_point_id_fk"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "whatsapp_number": {"name": "whatsapp_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "user_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'STUDENT'"}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.subject_status": {"name": "subject_status", "schema": "public", "values": ["PASS", "FAIL", "AB", "P", "F", "F(IN)", "F(PR)", "F(TH)"]}, "public.board_result_type": {"name": "board_result_type", "schema": "public", "values": ["FAIL", "PASS"]}, "public.transport_type": {"name": "transport_type", "schema": "public", "values": ["BUS", "TRAIN", "METRO", "AUTO", "TAXI", "CYCLE", "WALKING", "OTHER"]}, "public.admission_form_status": {"name": "admission_form_status", "schema": "public", "values": ["DRAFT", "PAYMENT_DUE", "PAYMENT_SUCCESS", "PAYMENT_FAILED", "SUBMITTED", "APPROVED", "REJECTED", "CANCELLED", "WAITING_FOR_APPROVAL", "WAITING_FOR_PAYMENT", "WAITING_FOR_DOCUMENTS", "DOCUMENTS_VERIFIED", "DOCUMENTS_PENDING", "DOCUMENTS_REJECTED"]}, "public.admission_steps": {"name": "admission_steps", "schema": "public", "values": ["GENERAL_INFORMATION", "ACADEMIC_INFORMATION", "COURSE_APPLICATION", "ADDITIONAL_INFORMATION", "DOCUMENTS", "PAYMENT", "REVIEW", "SUBMITTED"]}, "public.board_result_status_type": {"name": "board_result_status_type", "schema": "public", "values": ["PASS", "FAIL IN THEORY", "FAIL IN PRACTICAL", "FAIL"]}, "public.class_type": {"name": "class_type", "schema": "public", "values": ["YEAR", "SEMESTER"]}, "public.community_type": {"name": "community_type", "schema": "public", "values": ["GUJARATI", "NON-GUJARATI"]}, "public.degree_level_type": {"name": "degree_level_type", "schema": "public", "values": ["SECONDARY", "HIGHER_SECONDARY", "UNDER_GRADUATE", "POST_GRADUATE"]}, "public.disability_type": {"name": "disability_type", "schema": "public", "values": ["VISUAL", "HEARING_IMPAIRMENT", "VISUAL_IMPAIRMENT", "ORTHOPEDIC", "OTHER"]}, "public.framework_type": {"name": "framework_type", "schema": "public", "values": ["CCF", "CBCS"]}, "public.gender_type": {"name": "gender_type", "schema": "public", "values": ["MALE", "FEMALE", "OTHER"]}, "public.locality_type": {"name": "locality_type", "schema": "public", "values": ["RURAL", "URBAN"]}, "public.marksheet_source": {"name": "marksheet_source", "schema": "public", "values": ["FILE_UPLOAD", "ADDED"]}, "public.otp_type": {"name": "otp_type", "schema": "public", "values": ["FOR_PHONE", "FOR_EMAIL"]}, "public.paper_mode_type": {"name": "paper_mode_type", "schema": "public", "values": ["THEORETICAL", "PRACTICAL", "VIVA", "ASSIGNMENT", "PROJECT", "MCQ"]}, "public.parent_type": {"name": "parent_type", "schema": "public", "values": ["BOTH", "FATHER_ONLY", "MOTHER_ONLY"]}, "public.payment_mode": {"name": "payment_mode", "schema": "public", "values": ["CASH", "CHEQUE", "ONLINE"]}, "public.payment_status": {"name": "payment_status", "schema": "public", "values": ["PENDING", "COMPLETED", "FAILED", "REFUNDED", "CANCELLED"]}, "public.person_title_type": {"name": "person_title_type", "schema": "public", "values": ["MR", "MRS", "MS", "DR", "PROF", "REV", "OTHER"]}, "public.place_of_stay_type": {"name": "place_of_stay_type", "schema": "public", "values": ["OWN", "HOSTEL", "FAMILY_FRIENDS", "PAYING_GUEST", "RELATIVES"]}, "public.programme_type": {"name": "programme_type", "schema": "public", "values": ["HONOURS", "GENERAL"]}, "public.sports_level": {"name": "sports_level", "schema": "public", "values": ["NATIONAL", "STATE", "DISTRICT", "OTHERS"]}, "public.stream_type": {"name": "stream_type", "schema": "public", "values": ["SCIENCE", "COMMERCE", "HUMANITIES", "ARTS"]}, "public.student_fees_mapping_type": {"name": "student_fees_mapping_type", "schema": "public", "values": ["FULL", "INSTALMENT"]}, "public.study_material_availability_type": {"name": "study_material_availability_type", "schema": "public", "values": ["ALWAYS", "CURRENT_SESSION_ONLY", "COURSE_LEVEL", "BATCH_LEVEL"]}, "public.study_material_type": {"name": "study_material_type", "schema": "public", "values": ["FILE", "LINK"]}, "public.study_meta_type": {"name": "study_meta_type", "schema": "public", "values": ["RESOURCE", "WORKSHEET", "ASSIGNMENT", "PROJECT"]}, "public.subject_category_type": {"name": "subject_category_type", "schema": "public", "values": ["SPECIAL", "COMMON", "HONOURS", "GENERAL", "ELECTIVE"]}, "public.user_type": {"name": "user_type", "schema": "public", "values": ["ADMIN", "STUDENT", "TEACHER"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}