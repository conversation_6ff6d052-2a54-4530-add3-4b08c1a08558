{"name": "backend", "version": "1.0.0", "description": "This is a taskify-backend used for serving the taskify-app. This is powered by Node.JS", "keywords": ["user", "task", "template", "instance", "customer", "parent-company"], "type": "module", "main": "index.js", "scripts": {"start": "node --stack-size=8192 dist/index.js", "dev": "npx nodemon --watch src --exec tsx --stack-size=8192 src/index.ts", "build": "tsc && tsc-alias", "db:generate": "npm run build && npx drizzle-kit generate", "db:migrate": "npm run build && npx drizzle-kit migrate", "db:studio": "npm run build && npx drizzle-kit studio"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@types/socket.io": "^3.0.1", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.38.3", "drizzle-zod": "^0.6.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "module-alias": "^2.2.3", "multer": "^1.4.5-lts.1", "mysql2": "^3.12.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "pg": "^8.13.1", "socket.io": "^4.8.1", "uuid": "^11.0.3", "xlsx": "^0.18.5", "zeptomail": "^6.2.1", "zod": "^3.24.1"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/express-session": "^1.18.1", "@types/jsonwebtoken": "^9.0.8", "@types/node": "^22.10.2", "@types/passport-google-oauth20": "^2.0.16", "@types/pg": "^8.11.10", "drizzle-kit": "^0.30.1", "husky": "^9.1.7", "lint-staged": "^15.2.11", "nodemon": "^3.1.9", "prettier": "^3.4.2", "ts-node": "^10.9.2", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.2", "typescript": "^5.7.2"}, "lint-staged": {"**/*.{ts,js,json,md,css}": ["npx prettier --write .", "git add"]}, "prettier": {"overrides": [{"files": "*.{js,ts,json}", "options": {"tabWidth": 2}}]}}