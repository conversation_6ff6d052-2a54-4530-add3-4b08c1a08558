import { Router } from "express";
import {
  createExamComponent,
  getAllExamComponents,
  getExamComponentById,
  updateExamComponent,
  deleteExamComponent,
} from "../controllers/exam-component.controller";
import { RequestHandler } from "express";

const router = Router();

// Exam Component routes
router.post("/", createExamComponent as <PERSON>questHandler);
router.get("/", getAllExamComponents as RequestHandler);
router.get("/:id", getExamComponentById as RequestHandler);
router.put("/:id", updateExamComponent as <PERSON>questHandler);
router.delete("/:id", deleteExamComponent as <PERSON>questHandler);

export default router;
