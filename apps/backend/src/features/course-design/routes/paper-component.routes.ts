import { Router } from "express";
import {
  createPaperComponent,
  getAllPaperComponents,
  getPaperComponentById,
  updatePaperComponent,
  deletePaperComponent,
} from "../controllers/paper-component.controller";
import { RequestHandler } from "express";

const router = Router();

// Paper Component routes
router.post("/", createPaperComponent as <PERSON>questHandler);
router.get("/", getAllPaperComponents as RequestHandler);
router.get("/:id", getPaperComponentById as RequestHandler);
router.put("/:id", updatePaperComponent as <PERSON><PERSON><PERSON><PERSON><PERSON>);
router.delete("/:id", deletePaperComponent as <PERSON><PERSON><PERSON><PERSON><PERSON>);

export default router;
