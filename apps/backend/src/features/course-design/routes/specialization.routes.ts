import { Router } from "express";
import {
  createSpecialization,
  getAllSpecializations,
  getSpecializationById,
  updateSpecialization,
  deleteSpecialization,
} from "../controllers/specialization.controller";
import { RequestHandler } from "express";

const router = Router();

// Specialization routes
router.post("/", createSpecialization as <PERSON>quest<PERSON>andler);
router.get("/", getAllSpecializations as <PERSON>questHandler);
router.get("/:id", getSpecializationById as RequestHandler);
router.put("/:id", updateSpecialization as <PERSON>questHandler);
router.delete("/:id", deleteSpecialization as <PERSON>questHandler);

export default router;
