import { Router } from "express";
import {
  createSubjectType,
  getAllSubjectTypes,
  getSubjectTypeById,
  updateSubjectType,
  deleteSubjectType,
} from "../controllers/subject-type.controller";
import { RequestHandler } from "express";

const router = Router();

// Subject Type routes
router.post("/", createSubjectType as <PERSON>questHandler);
router.get("/", getAllSubjectTypes as RequestHandler);
router.get("/:id", getSubjectTypeById as RequestHandler);
router.put("/:id", updateSubjectType as RequestHandler);
router.delete("/:id", deleteSubjectType as <PERSON>questHandler);

export default router;
