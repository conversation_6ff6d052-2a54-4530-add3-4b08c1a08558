import academicHistoryRouter from '@/features/user/routes/academicHistory.route.js';
import academicIdentifierRouter from '@/features/user/routes/academicIdentifier.route.js';
import accommodationRouter from '@/features/user/routes/accomodation.route.js';
import addressRouter from '@/features/user/routes/address.route.js';
import admissionRouter from '@/features/admissions/routes/admission.route.js';
import emergencyContactRouter from '@/features/user/routes/emergencyContact.route.js';
import healthRouter from '@/features/user/routes/health.route.js';
import familyRouter from '@/features/user/routes/family.route.js';
import personRouter from '@/features/user/routes/person.route.js';
import personalDetailsRouter from '@/features/user/routes/personalDetails.route.js';
import transportDetailsRouter from '@/features/user/routes/transportDetails.route.js';
import userRouter from '@/features/user/routes/user.route.js';
import studentRouter from '@/features/user/routes/student.route.js';
import reportRouter from "@/features/user/routes/report.route.js";
import specializationRouter from "@/features/user/routes/specialization.routes.js";

export {
    academicHistoryRouter,
    academicIdentifierRouter,
    accommodationRouter,
    addressRouter,
    admissionRouter,
    emergencyContactRouter,
    healthRouter,
    familyRouter,
    personRouter,
    personalDetailsRouter,
    transportDetailsRouter,
    userRouter,
    studentRouter,
    reportRouter,
    specializationRouter
}